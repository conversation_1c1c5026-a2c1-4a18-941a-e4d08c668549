import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Optimize for demo performance
  experimental: {
    optimizePackageImports: ['@mui/material', '@mui/icons-material'],
  },

  // Enable compression for better demo loading
  compress: true,

  // Optimize images for demo assets
  images: {
    formats: ['image/webp', 'image/avif'],
  },

  // Configure for demo deployment
  output: 'standalone',

  // Network access configuration for LAN development
  // Allow access from other devices on the local network
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization',
          },
        ],
      },
    ];
  },

  // Silence the workspace root warning for demo
  // Note: turbo config moved to turbopack.root in newer versions
};

export default nextConfig;
