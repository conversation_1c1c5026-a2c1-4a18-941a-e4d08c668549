/**
 * Component Status API Endpoint
 * Purpose: Provide status and metrics for all 95+ M0 components
 * Shows component health, alerts, and operational status
 */

import type { NextApiRequest, NextApiResponse } from 'next';
import type { IComponentStatus, IComponentStatusResponse, IComponentAlert } from '../../../types/tracking.types';
import { ENV } from '../../../utils/env';

// Generate realistic component status data
const generateComponentStatuses = (): IComponentStatus[] => {
  const components: IComponentStatus[] = [];

  // Core component definitions
  const componentDefinitions = [
    {
      name: 'SessionTrackingCore',
      category: 'tracking' as const,
      baseHealthScore: 98,
    },
    {
      name: 'SessionTrackingAudit',
      category: 'tracking' as const,
      baseHealthScore: 96,
    },
    {
      name: 'GovernanceRuleEngine',
      category: 'governance' as const,
      baseHealthScore: 94,
    },
    {
      name: 'SecurityEnforcementLayer',
      category: 'security' as const,
      baseHealthScore: 92,
    },
    {
      name: 'IntegrationBridge',
      category: 'integration' as const,
      baseHealthScore: 99,
    },
    {
      name: 'FoundationCore',
      category: 'foundation' as const,
      baseHealthScore: 97,
    },
    {
      name: 'PerformanceMonitor',
      type: 'analytics' as const,
      isEnhanced: true,
      baseHealthScore: 95,
    },
    {
      name: 'ComponentHealthTracker',
      type: 'core' as const,
      isEnhanced: false,
      baseHealthScore: 93,
    },
    {
      name: 'OrchestrationCoordinator',
      type: 'core' as const,
      isEnhanced: true,
      baseHealthScore: 98,
    },
  ];

  // Generate core services
  coreServices.forEach((serviceConfig, index) => {
    const now = Date.now();
    const healthScore = serviceConfig.baseHealthScore + Math.random() * 2; // Small variation
    const uptime = 95 + Math.random() * 5; // 95-100% uptime
    
    services.push({
      id: `tracking-${index.toString().padStart(3, '0')}`,
      name: serviceConfig.name,
      type: serviceConfig.type,
      status: healthScore > 90 ? 'active' : 'degraded',
      version: `4.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      healthScore: Math.round(healthScore * 10) / 10,
      lastHealthCheck: new Date(now - Math.random() * 5 * 60 * 1000).toISOString(), // 0-5 minutes ago
      uptime: Math.round(uptime * 10) / 10,
      requestCount: Math.floor(Math.random() * 10000) + 1000,
      errorCount: Math.floor(Math.random() * 50),
      averageResponseTime: Math.round((50 + Math.random() * 200) * 10) / 10, // 50-250ms
      memoryUsage: Math.round((20 + Math.random() * 60) * 10) / 10, // 20-80MB
      cpuUsage: Math.round((5 + Math.random() * 25) * 10) / 10, // 5-30%
      dependencies: [], // Will be populated based on service type
      isEnhanced: serviceConfig.isEnhanced,
    });
  });

  // Generate additional services to reach 33+ tracking components
  const additionalServiceTypes = ['session', 'analytics', 'utils', 'core'] as const;
  const statuses = ['active', 'inactive', 'degraded', 'maintenance'] as const;

  for (let i = coreServices.length; i < ENV.TRACKING_COMPONENTS; i++) {
    const now = Date.now();
    const type = additionalServiceTypes[i % additionalServiceTypes.length];
    const healthScore = 85 + Math.random() * 15; // 85-100%
    const uptime = 90 + Math.random() * 10; // 90-100% uptime
    const isEnhanced = Math.random() > 0.3; // 70% enhanced
    
    services.push({
      id: `tracking-${i.toString().padStart(3, '0')}`,
      name: `TrackingService${i}`,
      type,
      status: i < 30 ? 'active' : statuses[Math.floor(Math.random() * statuses.length)], // Most active
      version: `4.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      healthScore: Math.round(healthScore * 10) / 10,
      lastHealthCheck: new Date(now - Math.random() * 10 * 60 * 1000).toISOString(), // 0-10 minutes ago
      uptime: Math.round(uptime * 10) / 10,
      requestCount: Math.floor(Math.random() * 5000) + 500,
      errorCount: Math.floor(Math.random() * 20),
      averageResponseTime: Math.round((30 + Math.random() * 300) * 10) / 10, // 30-330ms
      memoryUsage: Math.round((10 + Math.random() * 80) * 10) / 10, // 10-90MB
      cpuUsage: Math.round((2 + Math.random() * 30) * 10) / 10, // 2-32%
      dependencies: [],
      isEnhanced,
    });
  }

  return services;
};

export default function handler(
  req: NextApiRequest,
  res: NextApiResponse<ITrackingComponentsResponse | { error: string }>
) {
  // Add CORS headers for demo deployment
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Simulate demo-appropriate response time (2-4 seconds)
    const delay = 2000 + Math.random() * 2000;
    
    setTimeout(() => {
      if (req.method === 'GET') {
        // Handle filtering
        const status = req.query.status as string;
        const type = req.query.type as string;
        const enhanced = req.query.enhanced as string;

        let filteredServices = trackingServices;

        // Apply filters
        if (status) {
          filteredServices = filteredServices.filter(service => service.status === status);
        }
        if (type) {
          filteredServices = filteredServices.filter(service => service.type === type);
        }
        if (enhanced !== undefined) {
          const isEnhanced = enhanced === 'true';
          filteredServices = filteredServices.filter(service => service.isEnhanced === isEnhanced);
        }

        // Calculate summary statistics
        const total = filteredServices.length;
        const healthy = filteredServices.filter(s => s.status === 'active' && s.healthScore > 90).length;
        const degraded = filteredServices.filter(s => s.status === 'degraded' || (s.status === 'active' && s.healthScore <= 90)).length;
        const offline = filteredServices.filter(s => s.status === 'inactive').length;

        const response: ITrackingComponentsResponse = {
          components: filteredServices,
          total,
          healthy,
          degraded,
          offline,
          lastUpdate: new Date().toISOString(),
        };

        res.status(200).json(response);
      } else {
        res.status(405).json({ error: 'Method not allowed' });
      }
    }, delay);
  } catch (error) {
    console.error('Tracking components API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
}
