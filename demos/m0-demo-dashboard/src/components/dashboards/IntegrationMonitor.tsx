/**
 * Integration Testing Monitor Dashboard
 * Purpose: Comprehensive integration testing console for M0 demo
 * Features: Test results monitoring, suite management, system health tracking
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  Snackbar,
  CircularProgress,
  Paper,
  Chip,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  BugReport as TestIcon,
} from '@mui/icons-material';
import { useTrackingData } from '../../hooks/useRealTimeData';
import IntegrationTestingDisplay from '../widgets/IntegrationTestingDisplay';
import type { 
  IIntegrationTestResultsResponse,
  ITestSuitesResponse,
  ISystemHealthResponse
} from '../../types/integration.types';

interface IntegrationMonitorProps {
  className?: string;
}

export default function IntegrationMonitor({ className }: IntegrationMonitorProps) {
  const [mounted, setMounted] = useState(false);
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Real-time data hooks
  const { 
    data: testResultsData, 
    error: testResultsError, 
    isLoading: testResultsLoading,
    mutate: refreshTestResults
  } = useTrackingData<IIntegrationTestResultsResponse>('/api/integration/test-results', isRealTimeEnabled);

  const { 
    data: testSuitesData, 
    error: testSuitesError, 
    isLoading: testSuitesLoading,
    mutate: refreshTestSuites
  } = useTrackingData<ITestSuitesResponse>('/api/integration/test-suites', isRealTimeEnabled);

  const { 
    data: systemHealthData, 
    error: systemHealthError, 
    isLoading: systemHealthLoading,
    mutate: refreshSystemHealth
  } = useTrackingData<ISystemHealthResponse>('/api/integration/system-health', isRealTimeEnabled);

  // Hydration safety
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle real-time toggle
  const handleRealTimeToggle = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled);
    setSnackbarMessage(
      isRealTimeEnabled 
        ? 'Real-time updates paused' 
        : 'Real-time updates resumed'
    );
    setSnackbarOpen(true);
  };

  // Manual refresh all data
  const handleManualRefresh = async () => {
    try {
      await Promise.all([
        refreshTestResults(),
        refreshTestSuites(),
        refreshSystemHealth()
      ]);
      setLastRefresh(new Date());
      setSnackbarMessage('Integration data refreshed successfully');
      setSnackbarOpen(true);
    } catch {
      setSnackbarMessage('Failed to refresh integration data');
      setSnackbarOpen(true);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  if (!mounted) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
      </Box>
    );
  }

  const hasErrors = testResultsError || testSuitesError || systemHealthError;
  const isLoading = testResultsLoading || testSuitesLoading || systemHealthLoading;

  return (
    <Box className={className} sx={{ width: '100%', minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header Section */}
      <Paper elevation={1} sx={{ p: 3, mb: 3, bgcolor: 'white' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold' }}>
              🔗 Integration Testing Console
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Comprehensive integration testing and system health monitoring for M0 components
            </Typography>
          </Box>
          <Box display="flex" gap={2} alignItems="center">
            <Chip 
              label={`Last Update: ${lastRefresh.toLocaleTimeString()}`}
              variant="outlined"
              size="small"
            />
            <Button
              variant="outlined"
              startIcon={isRealTimeEnabled ? <PauseIcon /> : <PlayIcon />}
              onClick={handleRealTimeToggle}
              color={isRealTimeEnabled ? "secondary" : "primary"}
            >
              {isRealTimeEnabled ? 'Pause' : 'Resume'} Real-time
            </Button>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={handleManualRefresh}
              disabled={isLoading}
            >
              Refresh All
            </Button>
          </Box>
        </Box>

        {/* Error Alert */}
        {hasErrors && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            Some integration data sources are experiencing issues. Displaying cached data where available.
          </Alert>
        )}

        {/* Loading Indicator */}
        {isLoading && (
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <CircularProgress size={16} />
            <Typography variant="body2" color="text.secondary">
              Updating integration testing data...
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Main Integration Testing Display */}
      <Paper elevation={1} sx={{ mb: 3 }}>
        <IntegrationTestingDisplay
          testResultsData={testResultsData}
          testSuitesData={testSuitesData}
          systemHealthData={systemHealthData}
          loading={isLoading}
          error={hasErrors ? new Error('Integration data loading error') : null}
        />
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      />
    </Box>
  );
}
