/**
 * Security & Memory Safety Dashboard
 * Purpose: Complete security monitoring dashboard for M0 demo
 * Features: Real-time memory monitoring, attack simulation, component health
 */

'use client';

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  ButtonGroup,
  Alert,
  Snackbar,
  CircularProgress,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import Grid from '@mui/material/Grid';
import {
  Security as SecurityIcon,
  Memory as MemoryIcon,
  Shield as ShieldIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import MemoryUsageChart from '../widgets/MemoryUsageChart';
import ComponentHealthGrid from '../widgets/ComponentHealthGrid';
import type { 
  IMemoryUsageResponse, 
  IAttackSimulationResponse,
  IProtectionStatusResponse,
  IBoundaryEnforcementResponse 
} from '../../types/security.types';

// Import the real-time data hook
import { useSecurityData } from '../../hooks/useRealTimeData';

interface SecurityDashboardProps {
  className?: string;
}

export default function SecurityDashboard({ className }: SecurityDashboardProps) {
  const theme = useTheme();
  const [selectedService, setSelectedService] = useState<string>('');
  const [activeTab, setActiveTab] = useState(0);
  const [simulationRunning, setSimulationRunning] = useState(false);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState<'success' | 'warning' | 'error'>('success');

  // Fetch security data
  const { data: memoryData, loading: memoryLoading, error: memoryError } = 
    useSecurityData<IMemoryUsageResponse>('/security/memory-usage');
  
  const { data: protectionData, loading: protectionLoading } = 
    useSecurityData<IProtectionStatusResponse>('/security/protection-status');
  
  const { data: boundaryData, loading: boundaryLoading } = 
    useSecurityData<IBoundaryEnforcementResponse>('/security/boundary-enforcement');

  // Handle attack simulation
  const handleAttackSimulation = async (type: string) => {
    setSimulationRunning(true);
    try {
      const response = await fetch('/api/security/attack-simulation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type,
          intensity: 'medium',
          duration: 30,
          targetServices: selectedService ? [selectedService] : []
        })
      });
      
      if (response.ok) {
        setAlertMessage(`${type} simulation started successfully`);
        setAlertSeverity('success');
      } else {
        throw new Error('Simulation failed');
      }
    } catch (error) {
      setAlertMessage('Failed to start simulation');
      setAlertSeverity('error');
    } finally {
      setSimulationRunning(false);
      setAlertOpen(true);
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  if (memoryLoading && protectionLoading && boundaryLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (memoryError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load security data: {memoryError}
      </Alert>
    );
  }

  return (
    <Box className={className}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SecurityIcon color="primary" />
          Security & Memory Safety Dashboard
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Real-time monitoring of {memoryData?.services.length || 0} protected services with attack prevention
        </Typography>
      </Box>

      {/* Summary Cards */}
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
          gap: 3,
          mb: 3
        }}
      >
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Protected Services
                </Typography>
                <Typography variant="h4" color="success.main">
                  {memoryData?.summary.protectedServices || 0}
                </Typography>
              </Box>
              <ShieldIcon color="success" sx={{ fontSize: 40 }} />
            </Box>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Total Memory
                </Typography>
                <Typography variant="h4" color="primary.main">
                  {memoryData?.summary.totalMemoryUsage || 0} MB
                </Typography>
              </Box>
              <MemoryIcon color="primary" sx={{ fontSize: 40 }} />
            </Box>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Boundaries
                </Typography>
                <Typography variant="h4" color="info.main">
                  {memoryData?.summary.totalBoundaries || 0}
                </Typography>
              </Box>
              <SecurityIcon color="info" sx={{ fontSize: 40 }} />
            </Box>
          </CardContent>
        </Card>

        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Violations
                </Typography>
                <Typography variant="h4" color="warning.main">
                  {memoryData?.summary.totalViolations || 0}
                </Typography>
              </Box>
              <SecurityIcon color="warning" sx={{ fontSize: 40 }} />
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Attack Simulation Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Security Simulation Controls
          </Typography>
          <Box display="flex" gap={2} flexWrap="wrap">
            <Button
              variant="contained"
              color="error"
              startIcon={simulationRunning ? <CircularProgress size={16} color="inherit" /> : <PlayIcon />}
              onClick={() => handleAttackSimulation('memory-exhaustion')}
              disabled={simulationRunning}
            >
              Memory Attack
            </Button>
            <Button
              variant="contained"
              color="warning"
              startIcon={simulationRunning ? <CircularProgress size={16} color="inherit" /> : <PlayIcon />}
              onClick={() => handleAttackSimulation('buffer-overflow')}
              disabled={simulationRunning}
            >
              Buffer Overflow
            </Button>
            <Button
              variant="contained"
              color="info"
              startIcon={simulationRunning ? <CircularProgress size={16} color="inherit" /> : <PlayIcon />}
              onClick={() => handleAttackSimulation('stress-test')}
              disabled={simulationRunning}
            >
              Stress Test
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => window.location.reload()}
            >
              Refresh Data
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
          <Tab label="Memory Usage" icon={<MemoryIcon />} />
          <Tab label="Component Health" icon={<ShieldIcon />} />
          <Tab label="Protection Status" icon={<SecurityIcon />} />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {activeTab === 0 && memoryData && (
        <Box>
          <MemoryUsageChart
            services={memoryData.services}
            selectedService={selectedService}
            showThresholds={true}
            height={400}
          />
        </Box>
      )}

      {activeTab === 1 && memoryData && (
        <ComponentHealthGrid
          services={memoryData.services}
          onServiceSelect={setSelectedService}
          selectedService={selectedService}
        />
      )}

      {activeTab === 2 && protectionData && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Protection Status Overview
            </Typography>
            <Typography variant="body1">
              Protection coverage: {protectionData.overallProtection.protectionCoverage}%
            </Typography>
            <Typography variant="body1">
              Average health score: {protectionData.overallProtection.averageHealthScore}%
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Alert Snackbar */}
      <Snackbar
        open={alertOpen}
        autoHideDuration={6000}
        onClose={() => setAlertOpen(false)}
      >
        <Alert severity={alertSeverity} onClose={() => setAlertOpen(false)}>
          {alertMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
}
