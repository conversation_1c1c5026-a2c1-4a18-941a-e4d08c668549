/**
 * Governance Control Panel Dashboard
 * Purpose: Complete governance monitoring dashboard for M0 demo
 * Features: Real-time rule validation, compliance scoring, authority chain, audit trail
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Alert,
  Snackbar,
  CircularProgress,
  Tabs,
  Tab,
  Paper,
  Grid,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  Gavel as GovernanceIcon,
  Assessment as ComplianceIcon,
  AccountTree as AuthorityIcon,
  History as AuditIcon,
  Settings as RuleEngineIcon,
  PlayArrow as PlayIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useGovernanceData } from '../../hooks/useRealTimeData';
import GovernanceRulesList from '../widgets/GovernanceRulesList';
import ComplianceScoreCard from '../widgets/ComplianceScoreCard';
import AuthorityChainVisualization from '../widgets/AuthorityChainVisualization';
import AuditTrailViewer from '../widgets/AuditTrailViewer';
import RuleEngineInterface from '../widgets/RuleEngineInterface';
import type { 
  IGovernanceRulesResponse, 
  IComplianceResponse,
  IAuthorityChainResponse,
  IAuditTrailResponse
} from '../../types/governance.types';

interface GovernancePanelProps {
  className?: string;
}

export default function GovernancePanel({ className }: GovernancePanelProps) {
  const [selectedRule, setSelectedRule] = useState<string>('');
  const [activeTab, setActiveTab] = useState(0);
  const [operationRunning, setOperationRunning] = useState(false);
  const [alertOpen, setAlertOpen] = useState(false);
  const [alertMessage, setAlertMessage] = useState('');
  const [alertSeverity, setAlertSeverity] = useState<'success' | 'warning' | 'error'>('success');
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by only rendering after mount
  useEffect(() => {
    setMounted(true);
  }, []);

  // Fetch governance data
  const { data: rulesData, isLoading: rulesLoading, error: rulesError } = 
    useGovernanceData<IGovernanceRulesResponse>('/api/governance/rules');
  
  const { data: complianceData, isLoading: complianceLoading } = 
    useGovernanceData<IComplianceResponse>('/api/governance/compliance');
  
  const { data: authorityData, isLoading: authorityLoading } = 
    useGovernanceData<IAuthorityChainResponse>('/api/governance/authority-chain');
  
  const { data: auditData, isLoading: auditLoading } = 
    useGovernanceData<IAuditTrailResponse>('/api/governance/audit-trail');

  // Handle governance operations
  const handleGovernanceOperation = async (operation: string) => {
    setOperationRunning(true);
    try {
      const response = await fetch('/api/governance/operations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          operation,
          parameters: {
            ruleId: selectedRule || undefined,
            validateAll: operation === 'validate-all',
            authorityLevel: 'M0'
          }
        })
      });
      
      if (response.ok) {
        setAlertMessage(`${operation} operation completed successfully`);
        setAlertSeverity('success');
      } else {
        throw new Error('Operation failed');
      }
    } catch (error) {
      setAlertMessage('Failed to execute governance operation');
      setAlertSeverity('error');
    } finally {
      setOperationRunning(false);
      setAlertOpen(true);
    }
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  if (!mounted) {
    // Return a simple placeholder during SSR to prevent hydration mismatch
    return (
      <div style={{ minHeight: '400px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <div style={{ textAlign: 'center' }}>
          <h1 style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1f2937', marginBottom: '0.5rem' }}>
            📊 Governance Control Panel
          </h1>
          <p style={{ color: '#6b7280' }}>
            Loading governance monitoring dashboard...
          </p>
        </div>
      </div>
    );
  }

  if (rulesLoading && complianceLoading && authorityLoading && auditLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (rulesError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load governance data: {rulesError?.message || 'Unknown error'}
      </Alert>
    );
  }

  return (
    <Box className={className}>
      {/* Header */}
      <Box mb={3}>
        <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <GovernanceIcon color="primary" />
          Governance Control Panel
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Real-time governance rule validation across {rulesData?.total || 61}+ components with authority chain management
        </Typography>
      </Box>

      {/* Summary Cards */}
      <Box 
        sx={{ 
          display: 'grid', 
          gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
          gap: 3,
          mb: 3
        }}
      >
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Compliance Score
                </Typography>
                <Typography variant="h4" color="success.main">
                  {complianceData?.metrics.overallScore || 122}%
                </Typography>
              </Box>
              <ComplianceIcon color="success" sx={{ fontSize: 40 }} />
            </Box>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Active Rules
                </Typography>
                <Typography variant="h4" color="primary.main">
                  {rulesData?.rules.filter(r => r.status === 'active').length || 58}
                </Typography>
              </Box>
              <GovernanceIcon color="primary" sx={{ fontSize: 40 }} />
            </Box>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Authority Levels
                </Typography>
                <Typography variant="h4" color="info.main">
                  {authorityData?.chain.length || 3}
                </Typography>
              </Box>
              <AuthorityIcon color="info" sx={{ fontSize: 40 }} />
            </Box>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="space-between">
              <Box>
                <Typography color="textSecondary" gutterBottom>
                  Audit Entries
                </Typography>
                <Typography variant="h4" color="warning.main">
                  {auditData?.total || 247}
                </Typography>
              </Box>
              <AuditIcon color="warning" sx={{ fontSize: 40 }} />
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Governance Operations Controls */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Governance Operations
          </Typography>
          <Box display="flex" gap={2} flexWrap="wrap">
            <Button
              variant="contained"
              color="primary"
              startIcon={operationRunning ? <CircularProgress size={16} color="inherit" /> : <PlayIcon />}
              onClick={() => handleGovernanceOperation('validate-rules')}
              disabled={operationRunning}
            >
              Validate Rules
            </Button>
            <Button
              variant="contained"
              color="info"
              startIcon={operationRunning ? <CircularProgress size={16} color="inherit" /> : <PlayIcon />}
              onClick={() => handleGovernanceOperation('test-authority-chain')}
              disabled={operationRunning}
            >
              Test Authority Chain
            </Button>
            <Button
              variant="contained"
              color="success"
              startIcon={operationRunning ? <CircularProgress size={16} color="inherit" /> : <PlayIcon />}
              onClick={() => handleGovernanceOperation('validate-cross-references')}
              disabled={operationRunning}
            >
              Validate Cross-References
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={() => window.location.reload()}
            >
              Refresh Data
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange} variant="fullWidth">
          <Tab label="Governance Rules" icon={<GovernanceIcon />} />
          <Tab label="Compliance Score" icon={<ComplianceIcon />} />
          <Tab label="Authority Chain" icon={<AuthorityIcon />} />
          <Tab label="Audit Trail" icon={<AuditIcon />} />
          <Tab label="Rule Engine" icon={<RuleEngineIcon />} />
        </Tabs>
      </Paper>

      {/* Tab Content */}
      {activeTab === 0 && rulesData && (
        <GovernanceRulesList
          rules={rulesData.rules}
          onRuleSelect={setSelectedRule}
          selectedRule={selectedRule}
        />
      )}

      {activeTab === 1 && complianceData && (
        <ComplianceScoreCard
          metrics={complianceData.metrics}
          timestamp={complianceData.timestamp}
        />
      )}

      {activeTab === 2 && authorityData && (
        <AuthorityChainVisualization
          chain={authorityData.chain}
          validationStatus={authorityData.validationStatus}
        />
      )}

      {activeTab === 3 && auditData && (
        <AuditTrailViewer
          entries={auditData.entries}
          total={auditData.total}
        />
      )}

      {activeTab === 4 && (
        <RuleEngineInterface
          selectedRule={selectedRule}
          onOperationComplete={(message) => {
            setAlertMessage(message);
            setAlertSeverity('success');
            setAlertOpen(true);
          }}
        />
      )}

      {/* Alert Snackbar */}
      <Snackbar
        open={alertOpen}
        autoHideDuration={6000}
        onClose={() => setAlertOpen(false)}
      >
        <Alert severity={alertSeverity} onClose={() => setAlertOpen(false)}>
          {alertMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
}
