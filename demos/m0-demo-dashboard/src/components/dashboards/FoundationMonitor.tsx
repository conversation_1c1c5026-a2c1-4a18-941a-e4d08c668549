/**
 * Foundation Monitor Dashboard
 * Purpose: Comprehensive foundation monitoring console for M0 demo
 * Features: Infrastructure monitoring, dependency tracking, architecture overview
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Alert,
  Snackbar,
  CircularProgress,
  Paper,
  Chip,
  Card,
  CardContent,
  FormControlLabel,
  Switch,
  Divider
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
} from '@mui/icons-material';
import { useTrackingData } from '../../hooks/useRealTimeData';
import FoundationOverviewDisplay from '../widgets/FoundationOverviewDisplay';
import type { 
  IFoundationInfrastructureResponse,
  ISystemDependenciesResponse,
  IArchitectureOverviewResponse
} from '../../types/foundation.types';

interface FoundationMonitorProps {
  className?: string;
}

export default function FoundationMonitor({ className }: FoundationMonitorProps) {
  const [mounted, setMounted] = useState(false);
  const [isRealTimeEnabled, setIsRealTimeEnabled] = useState(true);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Real-time data hooks
  const { 
    data: infrastructureData, 
    error: infrastructureError, 
    isLoading: infrastructureLoading,
    mutate: refreshInfrastructure
  } = useTrackingData<IFoundationInfrastructureResponse>('/api/foundation/infrastructure', isRealTimeEnabled);

  const { 
    data: dependenciesData, 
    error: dependenciesError, 
    isLoading: dependenciesLoading,
    mutate: refreshDependencies
  } = useTrackingData<ISystemDependenciesResponse>('/api/foundation/dependencies', isRealTimeEnabled);

  const { 
    data: architectureData, 
    error: architectureError, 
    isLoading: architectureLoading,
    mutate: refreshArchitecture
  } = useTrackingData<IArchitectureOverviewResponse>('/api/foundation/architecture', isRealTimeEnabled);

  // Hydration safety
  useEffect(() => {
    setMounted(true);
  }, []);

  // Handle real-time toggle
  const handleRealTimeToggle = () => {
    setIsRealTimeEnabled(!isRealTimeEnabled);
    setSnackbarMessage(
      isRealTimeEnabled 
        ? 'Real-time updates paused' 
        : 'Real-time updates resumed'
    );
    setSnackbarOpen(true);
  };

  // Manual refresh all data
  const handleManualRefresh = async () => {
    try {
      await Promise.all([
        refreshInfrastructure(),
        refreshDependencies(),
        refreshArchitecture()
      ]);
      setLastRefresh(new Date());
      setSnackbarMessage('Foundation data refreshed successfully');
      setSnackbarOpen(true);
    } catch {
      setSnackbarMessage('Failed to refresh foundation data');
      setSnackbarOpen(true);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  if (!mounted) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress size={40} />
      </Box>
    );
  }

  const hasErrors = infrastructureError || dependenciesError || architectureError;
  const isLoading = infrastructureLoading || dependenciesLoading || architectureLoading;

  return (
    <Box className={className} sx={{ width: '100%', minHeight: '100vh', bgcolor: 'grey.50' }}>
      {/* Header Section */}
      <Paper elevation={1} sx={{ p: 3, mb: 3, bgcolor: 'white' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold' }}>
              🎯 Foundation Overview Dashboard
            </Typography>
            <Typography variant="subtitle1" color="text.secondary">
              Comprehensive foundation monitoring including infrastructure, dependencies, and architecture metrics
            </Typography>
          </Box>
          <Box display="flex" gap={2} alignItems="center">
            <Chip 
              label={`Last Update: ${lastRefresh.toLocaleTimeString()}`}
              variant="outlined"
              size="small"
            />
            <Button
              variant="outlined"
              startIcon={isRealTimeEnabled ? <PauseIcon /> : <PlayIcon />}
              onClick={handleRealTimeToggle}
              color={isRealTimeEnabled ? "secondary" : "primary"}
            >
              {isRealTimeEnabled ? 'Pause' : 'Resume'} Real-time
            </Button>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={handleManualRefresh}
              disabled={isLoading}
            >
              Refresh All
            </Button>
          </Box>
        </Box>

        {/* Error Alert */}
        {hasErrors && (
          <Alert severity="warning" sx={{ mb: 2 }}>
            Some foundation data sources are experiencing issues. Displaying cached data where available.
          </Alert>
        )}

        {/* Loading Indicator */}
        {isLoading && (
          <Box display="flex" alignItems="center" gap={1} mb={2}>
            <CircularProgress size={16} />
            <Typography variant="body2" color="text.secondary">
              Updating foundation monitoring data...
            </Typography>
          </Box>
        )}
      </Paper>

      {/* Main Foundation Overview Display */}
      <Paper elevation={1} sx={{ mb: 3 }}>
        <FoundationOverviewDisplay
          infrastructureData={infrastructureData}
          dependenciesData={dependenciesData}
          architectureData={architectureData}
          loading={isLoading}
          error={hasErrors ? new Error('Foundation data loading error') : null}
        />
      </Paper>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={4000}
        onClose={handleSnackbarClose}
        message={snackbarMessage}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      />
    </Box>
  );
}
