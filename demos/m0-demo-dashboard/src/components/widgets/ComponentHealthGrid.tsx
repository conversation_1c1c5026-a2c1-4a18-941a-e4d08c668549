/**
 * Component Health Grid Widget
 * Purpose: Grid view of all 22+ protected services with health indicators
 * Features: Service status, memory usage, protection level, alerts
 */

'use client';

import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Avatar,
  IconButton,
  Tooltip,
  LinearProgress,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import Grid from '@mui/material/Grid';
import {
  Security as SecurityIcon,
  Memory as MemoryIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Shield as ShieldIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import type { IMemorySafetyMetrics, ISecurityAlert } from '../../types/security.types';

interface ComponentHealthGridProps {
  services: IMemorySafetyMetrics[];
  onServiceSelect?: (serviceId: string) => void;
  selectedService?: string;
}

export default function ComponentHealthGrid({
  services,
  onServiceSelect,
  selectedService
}: ComponentHealthGridProps) {
  const theme = useTheme();
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [selectedServiceDetails, setSelectedServiceDetails] = useState<IMemorySafetyMetrics | null>(null);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'protected': return theme.palette.success.main;
      case 'monitoring': return theme.palette.warning.main;
      case 'vulnerable': return theme.palette.error.main;
      case 'offline': return theme.palette.grey[500];
      default: return theme.palette.grey[500];
    }
  };

  // Get protection level color
  const getProtectionColor = (level: string) => {
    switch (level) {
      case 'maximum': return theme.palette.error.main;
      case 'enterprise': return theme.palette.primary.main;
      case 'enhanced': return theme.palette.info.main;
      case 'basic': return theme.palette.warning.main;
      default: return theme.palette.grey[500];
    }
  };

  // Get service type icon
  const getServiceIcon = (type: string) => {
    switch (type) {
      case 'BaseTrackingService': return <SecurityIcon />;
      case 'MemorySafeResourceManager': return <MemoryIcon />;
      case 'Enhanced': return <ShieldIcon />;
      case 'Utility': return <SpeedIcon />;
      default: return <InfoIcon />;
    }
  };

  // Handle service click
  const handleServiceClick = (service: IMemorySafetyMetrics) => {
    if (onServiceSelect) {
      onServiceSelect(service.serviceId);
    }
    setSelectedServiceDetails(service);
    setDetailsOpen(true);
  };

  // Get memory usage color
  const getMemoryColor = (percentage: number) => {
    if (percentage >= 90) return 'error';
    if (percentage >= 75) return 'warning';
    if (percentage >= 50) return 'info';
    return 'success';
  };

  return (
    <>
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Component Health Grid ({services.length} Services)
          </Typography>
          
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr', lg: '1fr 1fr 1fr 1fr' },
              gap: 2
            }}
          >
            {services.map((service) => (
              <Box key={service.serviceId}>
                <Card
                  variant="outlined"
                  sx={{
                    cursor: 'pointer',
                    transition: 'all 0.2s',
                    border: selectedService === service.serviceId ? 2 : 1,
                    borderColor: selectedService === service.serviceId 
                      ? theme.palette.primary.main 
                      : theme.palette.divider,
                    '&:hover': {
                      boxShadow: 2,
                      transform: 'translateY(-2px)'
                    }
                  }}
                  onClick={() => handleServiceClick(service)}
                >
                  <CardContent sx={{ p: 2 }}>
                    {/* Service Header */}
                    <Box display="flex" alignItems="center" mb={1}>
                      <Badge
                        badgeContent={service.alerts.length}
                        color="error"
                        invisible={service.alerts.length === 0}
                      >
                        <Avatar
                          sx={{
                            bgcolor: getStatusColor(service.status),
                            width: 32,
                            height: 32,
                            mr: 1
                          }}
                        >
                          {getServiceIcon(service.type)}
                        </Avatar>
                      </Badge>
                      <Box flex={1}>
                        <Typography variant="subtitle2" noWrap>
                          {service.serviceName}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {service.type}
                        </Typography>
                      </Box>
                    </Box>

                    {/* Status Chips */}
                    <Box display="flex" gap={0.5} mb={1} flexWrap="wrap">
                      <Chip
                        label={service.status}
                        size="small"
                        sx={{
                          bgcolor: getStatusColor(service.status),
                          color: 'white',
                          fontSize: '0.7rem',
                          height: 20
                        }}
                      />
                      <Chip
                        label={service.protectionLevel}
                        size="small"
                        sx={{
                          bgcolor: getProtectionColor(service.protectionLevel),
                          color: 'white',
                          fontSize: '0.7rem',
                          height: 20
                        }}
                      />
                    </Box>

                    {/* Memory Usage */}
                    <Box mb={1}>
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="caption" color="textSecondary">
                          Memory Usage
                        </Typography>
                        <Typography variant="caption" fontWeight="bold">
                          {service.memoryUsage.current}MB / {service.memoryUsage.maximum}MB
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={service.memoryUsage.percentage}
                        color={getMemoryColor(service.memoryUsage.percentage)}
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                      <Typography variant="caption" color="textSecondary">
                        {service.memoryUsage.percentage}% used
                      </Typography>
                    </Box>

                    {/* Boundary Info */}
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="caption" color="textSecondary">
                        Boundaries: {service.boundaryEnforcement.boundaryCount}
                      </Typography>
                      {service.boundaryEnforcement.violationCount > 0 && (
                        <Tooltip title={`${service.boundaryEnforcement.violationCount} violations`}>
                          <WarningIcon color="warning" fontSize="small" />
                        </Tooltip>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Service Details Dialog */}
      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedServiceDetails && (
          <>
            <DialogTitle>
              <Box display="flex" alignItems="center" gap={1}>
                {getServiceIcon(selectedServiceDetails.type)}
                {selectedServiceDetails.serviceName}
                <Chip
                  label={selectedServiceDetails.status}
                  size="small"
                  sx={{
                    bgcolor: getStatusColor(selectedServiceDetails.status),
                    color: 'white'
                  }}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Memory Usage
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="Current Usage"
                        secondary={`${selectedServiceDetails.memoryUsage.current} MB`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Threshold"
                        secondary={`${selectedServiceDetails.memoryUsage.threshold} MB`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Maximum"
                        secondary={`${selectedServiceDetails.memoryUsage.maximum} MB`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Percentage"
                        secondary={`${selectedServiceDetails.memoryUsage.percentage}%`}
                      />
                    </ListItem>
                  </List>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Boundary Enforcement
                  </Typography>
                  <List dense>
                    <ListItem>
                      <ListItemText
                        primary="Enabled"
                        secondary={selectedServiceDetails.boundaryEnforcement.enabled ? 'Yes' : 'No'}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Boundary Count"
                        secondary={selectedServiceDetails.boundaryEnforcement.boundaryCount}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemText
                        primary="Violations"
                        secondary={selectedServiceDetails.boundaryEnforcement.violationCount}
                      />
                    </ListItem>
                    {selectedServiceDetails.boundaryEnforcement.lastViolation && (
                      <ListItem>
                        <ListItemText
                          primary="Last Violation"
                          secondary={new Date(selectedServiceDetails.boundaryEnforcement.lastViolation).toLocaleString()}
                        />
                      </ListItem>
                    )}
                  </List>
                </Grid>
                {selectedServiceDetails.alerts.length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant="h6" gutterBottom>
                      Active Alerts ({selectedServiceDetails.alerts.length})
                    </Typography>
                    <List dense>
                      {selectedServiceDetails.alerts.map((alert) => (
                        <ListItem key={alert.alertId}>
                          <ListItemIcon>
                            <WarningIcon color="warning" />
                          </ListItemIcon>
                          <ListItemText
                            primary={alert.message}
                            secondary={`${alert.type} - ${alert.severity} - ${new Date(alert.timestamp).toLocaleString()}`}
                          />
                        </ListItem>
                      ))}
                    </List>
                  </Grid>
                )}
              </Grid>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setDetailsOpen(false)}>
                Close
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </>
  );
}
