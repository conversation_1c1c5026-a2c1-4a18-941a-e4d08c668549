/**
 * Live Session Activity Feed Widget
 * Purpose: Activity feed displaying current session operations and status changes
 * Features: Real-time session events, filtering, status indicators
 */

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Avatar,
  Divider,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Paper,
  Badge,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ShowChart as ActivityIcon,
  Search as SearchIcon,
  Person as UserIcon,
  Computer as DeviceIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import type { ISessionTrackingResponse, ISessionData, ISessionEvent } from '../../types/tracking.types';

interface LiveSessionActivityFeedProps {
  data?: ISessionTrackingResponse;
  loading?: boolean;
  error?: Error | null;
}

interface ActivityItem {
  id: string;
  timestamp: string;
  type: 'session-start' | 'session-end' | 'page-view' | 'action' | 'error' | 'performance';
  title: string;
  description: string;
  status: 'success' | 'warning' | 'error' | 'info';
  sessionId: string;
  userId?: string;
  metadata?: Record<string, unknown>;
}

export default function LiveSessionActivityFeed({ data, loading, error }: LiveSessionActivityFeedProps) {
  const [mounted, setMounted] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [filteredActivities, setFilteredActivities] = useState<ActivityItem[]>([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Process session data into activity items
  useEffect(() => {
    if (!data?.sessions) return;

    const activityItems: ActivityItem[] = [];

    data.sessions.forEach(session => {
      // Add session start activity
      activityItems.push({
        id: `session-start-${session.sessionId}`,
        timestamp: session.startTime,
        type: 'session-start',
        title: 'Session Started',
        description: `New ${session.trackingType} session initiated`,
        status: 'info',
        sessionId: session.sessionId,
        userId: session.userId,
        metadata: session.metadata
      });

      // Add session events
      session.events.forEach(event => {
        activityItems.push({
          id: event.eventId,
          timestamp: event.timestamp,
          type: event.eventType as 'session-start' | 'session-end' | 'page-view' | 'action' | 'error' | 'performance',
          title: event.eventName,
          description: `${event.eventType} event in session ${session.sessionId.slice(-8)}`,
          status: event.success ? 'success' : 'error',
          sessionId: session.sessionId,
          userId: session.userId,
          metadata: event.eventData
        });
      });

      // Add session end activity if completed
      if (session.endTime) {
        activityItems.push({
          id: `session-end-${session.sessionId}`,
          timestamp: session.endTime,
          type: 'session-end',
          title: 'Session Completed',
          description: `Session duration: ${Math.round(session.duration / 1000)}s, ${session.activityCount} activities`,
          status: session.status === 'completed' ? 'success' : session.status === 'error' ? 'error' : 'warning',
          sessionId: session.sessionId,
          userId: session.userId,
          metadata: session.metadata
        });
      }
    });

    // Sort by timestamp (newest first)
    activityItems.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    
    setActivities(activityItems);
  }, [data]);

  // Filter activities based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredActivities(activities);
      return;
    }

    const filtered = activities.filter(activity =>
      activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      activity.sessionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (activity.userId && activity.userId.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    setFilteredActivities(filtered);
  }, [activities, searchTerm]);

  const getActivityIcon = (type: string, status: string) => {
    switch (type) {
      case 'session-start':
        return <UserIcon color="primary" />;
      case 'session-end':
        return <DeviceIcon color={status === 'success' ? 'success' : 'warning'} />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'performance':
        return <ActivityIcon color="info" />;
      default:
        return status === 'success' ? <SuccessIcon color="success" /> : <WarningIcon color="warning" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'success';
      case 'error': return 'error';
      case 'warning': return 'warning';
      default: return 'info';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  if (!mounted) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Failed to load session activity: {error.message}
      </Alert>
    );
  }

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="300px">
        <CircularProgress size={40} />
        <Typography variant="body2" sx={{ ml: 2 }}>
          Loading session activity...
        </Typography>
      </Box>
    );
  }

  if (!data) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        No session activity data available
      </Alert>
    );
  }

  return (
    <Box sx={{ width: '100%' }}>
      {/* Header with Stats */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h2" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
          <ActivityIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Live Session Activity Feed
        </Typography>
        <Box display="flex" gap={2}>
          <Chip 
            label={`${data.activeSessions} Active`} 
            color="success" 
            variant="outlined"
          />
          <Chip 
            label={`${data.totalSessions} Total Sessions`} 
            color="primary" 
            variant="outlined"
          />
        </Box>
      </Box>

      {/* Search and Filter */}
      <Card elevation={1} sx={{ mb: 3 }}>
        <CardContent>
          <TextField
            fullWidth
            size="small"
            placeholder="Search activities, sessions, or users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
            }}
          />
        </CardContent>
      </Card>

      {/* Activity Feed */}
      <Card elevation={2}>
        <CardContent sx={{ p: 0 }}>
          {filteredActivities.length === 0 ? (
            <Box p={4} textAlign="center">
              <Typography variant="body1" color="text.secondary">
                {searchTerm ? 'No activities match your search' : 'No recent activity'}
              </Typography>
            </Box>
          ) : (
            <List sx={{ maxHeight: '600px', overflow: 'auto' }}>
              {filteredActivities.slice(0, 50).map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem alignItems="flex-start">
                    <ListItemIcon>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: 'transparent' }}>
                        {getActivityIcon(activity.type, activity.status)}
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="subtitle2" component="span">
                            {activity.title}
                          </Typography>
                          <Chip 
                            label={activity.status.toUpperCase()} 
                            size="small" 
                            color={getStatusColor(activity.status) as 'primary' | 'secondary' | 'error' | 'info' | 'warning' | 'success' | 'default'}
                            variant="outlined"
                          />
                        </Box>
                      }
                      secondary={
                        <Box component="span">
                          <Typography variant="body2" color="text.secondary" component="span" display="block">
                            {activity.description}
                          </Typography>
                          <Box component="span" display="flex" alignItems="center" gap={2} mt={1}>
                            <Typography variant="caption" color="text.secondary" component="span">
                              Session: {activity.sessionId.slice(-8)}
                            </Typography>
                            {activity.userId && (
                              <Typography variant="caption" color="text.secondary" component="span">
                                User: {activity.userId}
                              </Typography>
                            )}
                            <Typography variant="caption" color="text.secondary" component="span">
                              {formatTimestamp(activity.timestamp)}
                            </Typography>
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < filteredActivities.length - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <Paper elevation={1} sx={{ p: 2, mt: 3, bgcolor: 'grey.50' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="body2" color="text.secondary">
            Showing {Math.min(filteredActivities.length, 50)} of {filteredActivities.length} activities
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Avg session duration: {Math.round(data.averageSessionDuration / 1000)}s
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
}
