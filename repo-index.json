[{"type": "directory", "name": "shared", "contents": [{"type": "directory", "name": "src", "contents": [{"type": "directory", "name": "base", "contents": [{"type": "directory", "name": "atomic-circular-buffer-enhanced", "contents": [{"type": "directory", "name": "modules", "contents": [{"type": "file", "name": "BufferAnalyticsEngine.ts"}, {"type": "file", "name": "BufferConfigurationManager.ts"}, {"type": "file", "name": "BufferOperationsManager.ts"}, {"type": "file", "name": "BufferPersistenceManager.ts"}, {"type": "file", "name": "BufferStrategyManager.ts"}, {"type": "file", "name": "BufferUtilities.ts"}]}, {"type": "file", "name": "performance-validation.ts"}]}, {"type": "file", "name": "AtomicCircularBufferEnhanced.ts"}, {"type": "file", "name": "AtomicCircularBuffer.ts"}, {"type": "directory", "name": "cleanup-coordinator-enhanced", "contents": [{"type": "directory", "name": "modules", "contents": [{"type": "file", "name": "AsyncErrorHandler.ts"}, {"type": "file", "name": "CleanupConfiguration.ts"}, {"type": "file", "name": "CleanupTemplateManager.ts"}, {"type": "file", "name": "CleanupUtilities.ts"}, {"type": "file", "name": "DependencyResolver.ts"}, {"type": "file", "name": "HealthStatusManager.ts"}, {"type": "file", "name": "InitializationManager.ts"}, {"type": "file", "name": "OperationExecutionManager.ts"}, {"type": "file", "name": "RollbackManager.ts"}, {"type": "file", "name": "RollbackSnapshots.ts"}, {"type": "file", "name": "RollbackUtilities.ts"}, {"type": "file", "name": "SystemOrchestrator.ts"}, {"type": "file", "name": "TemplateDependencies.ts"}, {"type": "file", "name": "TemplateValidation.ts"}, {"type": "file", "name": "TemplateWorkflows.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "AsyncErrorHandler.test.ts"}, {"type": "file", "name": "CleanupConfiguration.test.ts"}, {"type": "file", "name": "CleanupTemplateManager.test.ts"}, {"type": "file", "name": "CleanupUtilities.test.ts"}, {"type": "file", "name": "DependencyResolver.test.ts"}, {"type": "file", "name": "HealthStatusManager.test.ts"}, {"type": "file", "name": "InitializationManager.test.ts"}, {"type": "file", "name": "OperationExecutionManager.test.ts"}, {"type": "file", "name": "PerformanceValidation.test.ts"}, {"type": "file", "name": "RollbackManager-FinalBranches.test.ts"}, {"type": "file", "name": "RollbackManager.test.ts"}, {"type": "file", "name": "RollbackSnapshots-SurgicalCoverage.test.ts"}, {"type": "file", "name": "RollbackSnapshots.test.ts"}, {"type": "file", "name": "RollbackUtilities.test.ts"}, {"type": "file", "name": "SystemOrchestrator.test.ts"}, {"type": "file", "name": "TemplateDependencies.test.ts"}, {"type": "file", "name": "TemplateValidation-BranchCoverage.test.ts"}, {"type": "file", "name": "TemplateValidation-Enhanced.test.ts"}, {"type": "file", "name": "TemplateValidation-Final4Branches.test.ts"}, {"type": "file", "name": "TemplateValidation-FinalCoverage.test.ts"}, {"type": "file", "name": "TemplateValidation-SurgicalBranches.test.ts"}, {"type": "file", "name": "TemplateValidation-Surgical.test.ts"}, {"type": "file", "name": "TemplateValidation.test.ts"}, {"type": "file", "name": "TemplateWorkflows.test.ts"}, {"type": "file", "name": "TimingInfrastructureManager.test.ts"}, {"type": "file", "name": "UtilityAnalysis.test.ts"}, {"type": "file", "name": "UtilityExecution.test.ts"}, {"type": "file", "name": "UtilityPerformance.test.ts"}, {"type": "file", "name": "UtilityValidation.test.ts"}]}, {"type": "file", "name": "TimingInfrastructureManager.ts"}, {"type": "file", "name": "UtilityAnalysis.ts"}, {"type": "file", "name": "UtilityExecution.ts"}, {"type": "file", "name": "UtilityPerformance.ts"}, {"type": "file", "name": "UtilityValidation.ts"}]}]}, {"type": "file", "name": "CleanupCoordinatorEnhanced.ts"}, {"type": "directory", "name": "event-handler-registry", "contents": [{"type": "directory", "name": "modules", "contents": [{"type": "file", "name": "ComplianceManager.ts"}, {"type": "file", "name": "DeduplicationEngine.ts"}, {"type": "file", "name": "EventBuffering.ts"}, {"type": "file", "name": "EventEmissionSystem.ts"}, {"type": "file", "name": "EventUtilities.ts"}, {"type": "file", "name": "MetricsManager.ts"}, {"type": "file", "name": "MiddlewareManager.ts"}]}, {"type": "directory", "name": "types", "contents": [{"type": "file", "name": "EventConfiguration.ts"}, {"type": "file", "name": "EventHandlerEnhancedTypes.ts"}, {"type": "file", "name": "EventTypes.ts"}]}]}, {"type": "file", "name": "EventHandlerRegistryEnhanced.ts"}, {"type": "file", "name": "EventHandlerRegistry.ts"}, {"type": "file", "name": "LoggingMixin.ts"}, {"type": "file", "name": "MemorySafeResourceManagerEnhanced.ts"}, {"type": "file", "name": "MemorySafeResourceManager.ts"}, {"type": "directory", "name": "memory-safety-manager", "contents": [{"type": "directory", "name": "modules", "contents": [{"type": "file", "name": "ComponentDiscoveryManager.ts"}, {"type": "file", "name": "ComponentIntegrationEngine.ts"}, {"type": "file", "name": "EnhancedConfigurationManager.ts"}, {"type": "file", "name": "EnhancedMetricsCollector.ts"}, {"type": "file", "name": "SystemCoordinationManager.ts"}, {"type": "file", "name": "SystemStateManager.ts"}]}]}, {"type": "file", "name": "MemorySafetyManagerEnhanced.ts"}, {"type": "file", "name": "MemorySafetyManager.ts"}, {"type": "file", "name": "refactoring-prompt.md"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "AtomicCircularBuffer.branches.test.ts"}, {"type": "file", "name": "AtomicCircularBuffer.core.test.ts"}, {"type": "file", "name": "AtomicCircularBufferEnhanced.test.ts"}, {"type": "file", "name": "AtomicCircularBuffer.integration.test.ts"}, {"type": "file", "name": "AtomicCircularBuffer.memory.test.ts"}, {"type": "file", "name": "AtomicCircularBuffer.performance.test.ts"}, {"type": "file", "name": "AtomicCircularBuffer.test.ts"}, {"type": "file", "name": "BufferConfigurationManager.priority-coverage.test.ts"}, {"type": "file", "name": "CleanupCoordinatorEnhanced.branches.test.ts"}, {"type": "file", "name": "CleanupCoordinatorEnhanced.test.ts"}, {"type": "directory", "name": "e2e", "contents": [{"type": "file", "name": "complete-lifecycle.test.ts"}, {"type": "file", "name": "production-simulation.test.ts"}]}, {"type": "file", "name": "EnhancedMetricsCollector.priority-coverage.test.ts"}, {"type": "file", "name": "enhanced-services-integration.test.ts"}, {"type": "file", "name": "EnterpriseErrorHandling.coverage-boost.test.ts"}, {"type": "file", "name": "EventBuffering.branches-boost.test.ts"}, {"type": "file", "name": "EventBuffering.final-branches.test.ts"}, {"type": "file", "name": "EventBuffering.more-branches.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.additional-coverage.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.branches.test.ts"}, {"type": "file", "name": "EventHandlerRegistryEnhanced.priority-coverage.test.ts"}, {"type": "file", "name": "EventHandlerRegistryEnhanced.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.functions-boost-2.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.functions-boost-3.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.functions-boost.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.functions-interval-callbacks.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.functions.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.integration.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.memory-leak.test.ts"}, {"type": "file", "name": "EventHandlerRegistry.test.ts"}, {"type": "file", "name": "JestTestingUtils.ts"}, {"type": "file", "name": "LoggingMixin.additional-coverage.test.ts"}, {"type": "file", "name": "LoggingMixin.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.additional-branches-2.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.additional-branches.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.branches-boost.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.branches.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.coverage-boost-100.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.coverage-boost-95b.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.coverage-boost-95c.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.coverage-boost-95.test.ts"}, {"type": "file", "name": "MemorySafeResourceManagerEnhanced.integration.test.ts"}, {"type": "file", "name": "MemorySafeResourceManagerEnhanced.performance.test.ts"}, {"type": "file", "name": "MemorySafeResourceManagerEnhanced.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.more-branches.test.ts"}, {"type": "file", "name": "MemorySafeResourceManager.test.ts"}, {"type": "file", "name": "MemorySafeSystem.integration.test.ts"}, {"type": "file", "name": "MemorySafetyManagerEnhanced.test.ts"}, {"type": "file", "name": "MemorySafetyManager.priority-coverage.test.ts"}, {"type": "file", "name": "MiddlewareManager.branches-boost.test.ts"}, {"type": "file", "name": "MiddlewareManager.coverage-boost-95.test.ts"}, {"type": "file", "name": "MiddlewareManager.final-branches.test.ts"}, {"type": "file", "name": "MiddlewareManager.more-branches.test.ts"}, {"type": "directory", "name": "modules", "contents": [{"type": "directory", "name": "atomic-circular-buffer", "contents": [{"type": "file", "name": "BufferAnalyticsEngine.test.ts"}, {"type": "file", "name": "BufferOperationsManager.test.ts"}, {"type": "file", "name": "BufferPersistenceManager.test.ts"}, {"type": "file", "name": "BufferStrategyManager.test.ts"}, {"type": "file", "name": "BufferUtilities-Simple.test.ts"}]}, {"type": "directory", "name": "event-handler-registry", "contents": [{"type": "file", "name": "ComplianceManager.test.ts"}, {"type": "file", "name": "DeduplicationEngine.test.ts"}, {"type": "file", "name": "EventBuffering.test.ts"}, {"type": "file", "name": "EventConfiguration.test.ts"}, {"type": "file", "name": "EventEmissionSystem.test.ts"}, {"type": "file", "name": "EventTypes.test.ts"}, {"type": "file", "name": "EventUtilities.test.ts"}, {"type": "file", "name": "EventValidation.test.ts"}, {"type": "file", "name": "MetricsManager.test.ts"}, {"type": "file", "name": "MiddlewareManager.test.ts"}]}, {"type": "directory", "name": "memory-safety-manager", "contents": [{"type": "file", "name": "ComponentDiscoveryManager.test.ts"}, {"type": "file", "name": "ComponentIntegrationEngine.test.ts"}, {"type": "file", "name": "EnhancedConfigurationManager.test.ts"}, {"type": "file", "name": "SystemCoordinationManager.test.ts"}, {"type": "file", "name": "SystemStateManager.test.ts"}]}, {"type": "directory", "name": "timer-coordination", "contents": [{"type": "file", "name": "AdvancedScheduler.test.ts"}, {"type": "file", "name": "PhaseIntegration.test.ts"}, {"type": "file", "name": "TimerConfiguration.test.ts"}, {"type": "file", "name": "TimerCoordinationPatterns.test.ts"}, {"type": "file", "name": "TimerPoolManager.test.ts"}, {"type": "file", "name": "TimerUtilities.test.ts"}]}]}, {"type": "directory", "name": "performance", "contents": [{"type": "file", "name": "enhanced-services-performance.test.ts"}, {"type": "file", "name": "load-performance.test.ts"}, {"type": "file", "name": "memory-performance.test.ts"}, {"type": "file", "name": "performance-integration.test.ts"}, {"type": "file", "name": "resilient-timing-performance.test.ts"}]}, {"type": "file", "name": "ResilientTiming.coverage-boost.test.ts"}, {"type": "file", "name": "ResilientTiming.final-coverage.test.ts"}, {"type": "file", "name": "ResilientTiming.more-coverage.test.ts"}, {"type": "directory", "name": "scalability", "contents": [{"type": "file", "name": "timer-scalability-harness.js"}]}, {"type": "file", "name": "SystemCoordinationManager.additional-coverage.test.ts"}, {"type": "file", "name": "SystemCoordinationManager.branches.test.ts"}, {"type": "file", "name": "SystemCoordinationManager.more-branches.test.ts"}, {"type": "file", "name": "SystemCoordinationManager.production-path.test.ts"}, {"type": "file", "name": "SystemCoordinationManager.production-shutdown-path.test.ts"}, {"type": "file", "name": "TimerCoordinationService.additional-coverage.test.ts"}, {"type": "file", "name": "TimerCoordinationService.branches-boost.test.ts"}, {"type": "file", "name": "TimerCoordinationService.branches.test.ts"}, {"type": "file", "name": "TimerCoordinationServiceEnhanced.priority-coverage.test.ts"}, {"type": "file", "name": "TimerCoordinationServiceEnhanced.test.ts"}, {"type": "file", "name": "TimerCoordinationService.functions.test.ts"}, {"type": "file", "name": "TimerCoordinationService.more-functions.test.ts"}, {"type": "file", "name": "TimerCoordinationService.test.ts"}, {"type": "directory", "name": "timing", "contents": [{"type": "file", "name": "timing-context-validation.test.ts"}, {"type": "file", "name": "timing-reliability.test.ts"}]}, {"type": "directory", "name": "types"}, {"type": "directory", "name": "utils", "contents": [{"type": "file", "name": "EnterpriseErrorHandling.test.ts"}, {"type": "file", "name": "JestCompatibilityUtils.test.ts"}, {"type": "file", "name": "ResilientMetrics.test.ts"}, {"type": "file", "name": "ResilientTiming.getCurrentTime.direct.test.ts"}, {"type": "file", "name": "ResilientTiming.production-isolated.test.ts"}, {"type": "file", "name": "ResilientTiming.production-path.test.ts"}, {"type": "file", "name": "ResilientTiming.test.ts"}]}]}, {"type": "directory", "name": "timer-coordination", "contents": [{"type": "directory", "name": "modules", "contents": [{"type": "file", "name": "AdvancedScheduler.ts"}, {"type": "file", "name": "PhaseIntegration.ts"}, {"type": "file", "name": "TimerConfiguration.ts"}, {"type": "file", "name": "TimerCoordinationPatterns.ts"}, {"type": "file", "name": "TimerPoolManager.ts"}, {"type": "file", "name": "TimerUtilities.ts"}]}, {"type": "directory", "name": "types", "contents": [{"type": "file", "name": "TimerTypes.ts"}]}]}, {"type": "file", "name": "TimerCoordinationServiceEnhanced.ts"}, {"type": "file", "name": "TimerCoordinationService.ts"}, {"type": "directory", "name": "types", "contents": [{"type": "file", "name": "CleanupTypes.ts"}]}, {"type": "directory", "name": "utils", "contents": [{"type": "file", "name": "EnterpriseErrorHandling.ts"}, {"type": "file", "name": "JestCompatibilityUtils.ts"}, {"type": "file", "name": "ResilientMetrics.ts"}, {"type": "file", "name": "ResilientTiming.ts"}]}]}, {"type": "directory", "name": "constants", "contents": [{"type": "directory", "name": "platform", "contents": [{"type": "directory", "name": "tracking", "contents": [{"type": "file", "name": "environment-constants-calculator.ts"}, {"type": "file", "name": "tracking-constants-enhanced.ts"}, {"type": "file", "name": "tracking-constants.ts"}]}]}, {"type": "directory", "name": "tracking", "contents": [{"type": "file", "name": "tracking-management-constants.ts"}]}]}, {"type": "directory", "name": "interfaces", "contents": [{"type": "directory", "name": "governance", "contents": [{"type": "directory", "name": "management-configuration", "contents": [{"type": "file", "name": "governance-rule-documentation-generator.ts"}, {"type": "file", "name": "governance-rule-environment-manager.ts"}]}]}, {"type": "directory", "name": "tracking", "contents": [{"type": "file", "name": "core-interfaces.ts"}, {"type": "file", "name": "notification-interfaces.ts"}, {"type": "file", "name": "tracking-interfaces.ts"}]}]}, {"type": "directory", "name": "testing-utilities", "contents": [{"type": "file", "name": "SurgicalPrecisionTestUtils.ts"}]}, {"type": "directory", "name": "types", "contents": [{"type": "directory", "name": "platform", "contents": [{"type": "directory", "name": "governance", "contents": [{"type": "directory", "name": "automation-engines", "contents": [{"type": "file", "name": "workflow-engines-types.ts"}]}, {"type": "file", "name": "automation-processing-types.ts"}, {"type": "file", "name": "governance-interfaces.ts"}, {"type": "file", "name": "governance-types.ts"}, {"type": "directory", "name": "management-configuration", "contents": [{"type": "file", "name": "documentation-generator-types.ts"}, {"type": "file", "name": "environment-manager-types.ts"}]}, {"type": "file", "name": "notification-types.ts"}, {"type": "file", "name": "resource-interfaces.ts"}, {"type": "file", "name": "rule-management-types.ts"}, {"type": "file", "name": "security-types.ts"}]}, {"type": "directory", "name": "tracking", "contents": [{"type": "directory", "name": "core", "contents": [{"type": "file", "name": "base-types.ts"}, {"type": "file", "name": "tracking-config-types.ts"}, {"type": "file", "name": "tracking-data-types.ts"}, {"type": "file", "name": "tracking-service-types.ts"}]}, {"type": "file", "name": "index.ts"}, {"type": "directory", "name": "specialized", "contents": [{"type": "file", "name": "analytics-types.ts"}, {"type": "file", "name": "authority-types.ts"}, {"type": "file", "name": "orchestration-types.ts"}, {"type": "file", "name": "realtime-types.ts"}, {"type": "file", "name": "validation-types.ts"}]}, {"type": "file", "name": "tracking-types.ts"}, {"type": "directory", "name": "utilities", "contents": [{"type": "file", "name": "error-types.ts"}, {"type": "file", "name": "metrics-types.ts"}, {"type": "file", "name": "workflow-types.ts"}]}]}]}, {"type": "directory", "name": "tracking", "contents": [{"type": "file", "name": "core-types.ts"}, {"type": "file", "name": "tracking-management-types.ts"}]}]}]}]}, {"type": "report", "directories": 49, "files": 243}, {"type": "directory", "name": "server", "contents": [{"type": "directory", "name": "src", "contents": [{"type": "directory", "name": "errors", "contents": [{"type": "file", "name": "security-errors.ts"}]}, {"type": "file", "name": "index.ts"}, {"type": "directory", "name": "interfaces", "contents": [{"type": "directory", "name": "configuration", "contents": [{"type": "file", "name": "configuration-interfaces.ts"}]}, {"type": "directory", "name": "logging", "contents": [{"type": "file", "name": "logging-interfaces.ts"}]}, {"type": "directory", "name": "monitoring", "contents": [{"type": "file", "name": "monitoring-interfaces.ts"}]}, {"type": "directory", "name": "security", "contents": [{"type": "file", "name": "audit-interfaces.ts"}, {"type": "file", "name": "authorization-interfaces.ts"}, {"type": "file", "name": "crypto-interfaces.ts"}, {"type": "file", "name": "framework-interfaces.ts"}, {"type": "file", "name": "hash-interfaces.ts"}, {"type": "file", "name": "integrity-interfaces.ts"}, {"type": "file", "name": "security-interfaces.ts"}]}, {"type": "directory", "name": "storage", "contents": [{"type": "file", "name": "storage-interfaces.ts"}]}]}, {"type": "directory", "name": "platform", "contents": [{"type": "directory", "name": "governance", "contents": [{"type": "directory", "name": "analytics-engines", "contents": [{"type": "file", "name": "GovernanceRuleAnalyticsEngineFactory.ts"}, {"type": "file", "name": "GovernanceRuleAnalyticsEngine.ts"}, {"type": "file", "name": "GovernanceRuleInsightsGeneratorFactory.ts"}, {"type": "file", "name": "GovernanceRuleInsightsGenerator.ts"}, {"type": "file", "name": "GovernanceRuleOptimizationEngineFactory.ts"}, {"type": "file", "name": "GovernanceRuleOptimizationEngine.ts"}, {"type": "file", "name": "GovernanceRuleReportingEngineFactory.ts"}, {"type": "file", "name": "GovernanceRuleReportingEngine.ts"}, {"type": "file", "name": "index.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "GovernanceRuleAnalyticsEngineFactory.test.ts"}, {"type": "file", "name": "GovernanceRuleAnalyticsEngine.test.ts"}, {"type": "file", "name": "GovernanceRuleInsightsGeneratorFactory.test.ts"}, {"type": "file", "name": "GovernanceRuleInsightsGenerator.test.ts"}, {"type": "file", "name": "GovernanceRuleOptimizationEngineFactory.test.ts"}, {"type": "file", "name": "GovernanceRuleOptimizationEngine.test.ts"}, {"type": "file", "name": "GovernanceRuleReportingEngineFactory.test.ts"}, {"type": "file", "name": "GovernanceRuleReportingEngine.test.ts"}, {"type": "directory", "name": "integration", "contents": [{"type": "file", "name": "optimization-integration.test.ts"}]}]}]}, {"type": "directory", "name": "automation-engines", "contents": [{"type": "file", "name": "governance-rule-automation-engine.ts"}, {"type": "file", "name": "governance-rule-processing-engine.ts"}, {"type": "file", "name": "governance-rule-scheduling-engine.ts"}, {"type": "file", "name": "governance-rule-workflow-engine.ts"}]}, {"type": "directory", "name": "automation-processing", "contents": [{"type": "directory", "name": "errors", "contents": [{"type": "file", "name": "ProcessingErrors.ts"}]}, {"type": "directory", "name": "factories", "contents": [{"type": "file", "name": "RuleAuditLoggerFactory.ts"}]}, {"type": "file", "name": "GovernanceRuleEventManager.ts"}, {"type": "file", "name": "GovernanceRuleMaintenanceScheduler.ts"}, {"type": "file", "name": "GovernanceRuleNotificationSystemAutomation.ts"}, {"type": "file", "name": "GovernanceRuleTransformationEngine.ts"}]}, {"type": "directory", "name": "compliance-infrastructure", "contents": [{"type": "file", "name": "GovernanceRuleComplianceChecker.ts"}, {"type": "file", "name": "GovernanceRuleComplianceFramework.ts"}, {"type": "file", "name": "GovernanceRuleQualityFramework.ts"}, {"type": "file", "name": "GovernanceRuleTestingFramework.ts"}, {"type": "file", "name": "index.ts"}]}, {"type": "directory", "name": "continuity-backup", "contents": [{"type": "file", "name": "GovernanceRuleBackupManagerContinuity.ts"}, {"type": "file", "name": "GovernanceRuleDisasterRecovery.ts"}, {"type": "file", "name": "GovernanceRuleFailoverManager.ts"}, {"type": "file", "name": "GovernanceRuleRecoveryManager.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "GovernanceRuleBackupManagerContinuity.test.ts"}, {"type": "file", "name": "GovernanceRuleDisasterRecovery.test.ts"}, {"type": "file", "name": "GovernanceRuleFailoverManager.test.ts"}, {"type": "file", "name": "GovernanceRuleRecoveryManager.test.ts"}]}]}, {"type": "directory", "name": "enterprise-frameworks", "contents": [{"type": "file", "name": "GovernanceRuleEnterpriseFramework.ts"}, {"type": "file", "name": "GovernanceRuleGovernanceFramework.ts"}, {"type": "file", "name": "GovernanceRuleIntegrationFramework.ts"}, {"type": "file", "name": "GovernanceRuleManagementFramework.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "GovernanceRuleEnterpriseFramework.test.ts"}, {"type": "file", "name": "GovernanceRuleGovernanceFramework.test.ts"}, {"type": "file", "name": "GovernanceRuleIntegrationFramework.test.ts"}, {"type": "file", "name": "GovernanceRuleManagementFramework.test.ts"}]}]}, {"type": "directory", "name": "management-configuration", "contents": [{"type": "file", "name": "GovernanceRuleConfigurationManager.ts"}, {"type": "file", "name": "GovernanceRuleCSRFManager.ts"}, {"type": "file", "name": "GovernanceRuleDocumentationGenerator.ts"}, {"type": "file", "name": "GovernanceRuleEnvironmentManager.ts"}, {"type": "file", "name": "GovernanceRuleInputValidator.ts"}, {"type": "file", "name": "GovernanceRuleSecurityPolicy.ts"}, {"type": "file", "name": "GovernanceRuleTemplateEngine.ts"}, {"type": "file", "name": "GovernanceRuleTemplateSecurity.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "GovernanceRuleConfigurationManager.test.ts"}, {"type": "file", "name": "GovernanceRuleCSRFManager.test.ts"}, {"type": "file", "name": "GovernanceRuleDocumentationGenerator.test.ts"}, {"type": "file", "name": "GovernanceRuleEnvironmentManager.test.ts"}, {"type": "file", "name": "GovernanceRuleInputValidator.test.ts"}, {"type": "file", "name": "GovernanceRuleSecurityPolicy.test.ts"}, {"type": "file", "name": "GovernanceRuleTemplateEngine.test.ts"}, {"type": "file", "name": "GovernanceRuleTemplateSecurity.test.ts"}]}]}, {"type": "directory", "name": "performance-management", "contents": [{"type": "directory", "name": "analytics", "contents": [{"type": "file", "name": "RulePerformanceProfiler.ts"}]}, {"type": "directory", "name": "cache", "contents": [{"type": "file", "name": "RuleCacheManager.ts"}, {"type": "file", "name": "RuleResourceManager.ts"}]}, {"type": "directory", "name": "monitoring", "contents": [{"type": "file", "name": "RuleHealthChecker.ts"}, {"type": "file", "name": "RuleMetricsCollector.ts"}, {"type": "file", "name": "RuleMonitoringSystem.ts"}, {"type": "file", "name": "RuleNotificationSystem.ts"}]}, {"type": "directory", "name": "optimization", "contents": [{"type": "file", "name": "RulePerformanceOptimizer.ts"}]}]}, {"type": "directory", "name": "reporting-infrastructure", "contents": [{"type": "file", "name": "GovernanceRuleAlertManagerFactory.ts"}, {"type": "file", "name": "GovernanceRuleAlertManager.ts"}, {"type": "file", "name": "GovernanceRuleComplianceReporterFactory.ts"}, {"type": "file", "name": "GovernanceRuleComplianceReporter.ts"}, {"type": "file", "name": "GovernanceRuleDashboardGeneratorFactory.ts"}, {"type": "file", "name": "GovernanceRuleDashboardGenerator.ts"}, {"type": "file", "name": "GovernanceRuleReportSchedulerFactory.ts"}, {"type": "file", "name": "GovernanceRuleReportScheduler.ts"}, {"type": "file", "name": "index.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "GovernanceRuleAlertManagerFactory.test.ts"}, {"type": "file", "name": "GovernanceRuleAlertManager.test.ts"}, {"type": "file", "name": "GovernanceRuleComplianceReporter.test.ts"}, {"type": "file", "name": "GovernanceRuleDashboardGeneratorFactory.test.ts"}, {"type": "file", "name": "GovernanceRuleDashboardGenerator.test.ts"}, {"type": "file", "name": "GovernanceRuleReportSchedulerFactory.test.ts"}, {"type": "file", "name": "GovernanceRuleReportScheduler.test.ts"}]}]}, {"type": "directory", "name": "rule-management", "contents": [{"type": "directory", "name": "compliance", "contents": [{"type": "file", "name": "GovernanceAuthorityValidator.ts"}, {"type": "file", "name": "GovernanceComplianceChecker.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "GovernanceAuthorityValidator.test.ts"}, {"type": "file", "name": "GovernanceComplianceChecker.test.ts"}]}]}, {"type": "directory", "name": "core", "contents": [{"type": "file", "name": "GovernanceRuleEngineCore.ts"}, {"type": "file", "name": "GovernanceRuleExecutionContext.ts"}, {"type": "file", "name": "GovernanceRuleValidatorFactory.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "GovernanceRuleEngineCore.test.ts"}, {"type": "file", "name": "GovernanceRuleExecutionContext.test.ts"}, {"type": "file", "name": "GovernanceRuleValidatorFactory.test.ts"}]}]}, {"type": "directory", "name": "infrastructure", "contents": [{"type": "file", "name": "GovernanceRuleAuditLogger.ts"}, {"type": "file", "name": "GovernanceRuleCacheManager.ts"}, {"type": "file", "name": "GovernanceRuleMetricsCollector.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "GovernanceRuleAuditLogger.test.ts"}, {"type": "file", "name": "GovernanceRuleCacheManager.test.ts"}, {"type": "file", "name": "GovernanceRuleMetricsCollector.test.ts"}]}]}, {"type": "file", "name": "RuleConflictResolutionEngine.ts"}, {"type": "file", "name": "RuleDependencyGraphAnalyzer.ts"}, {"type": "file", "name": "RuleExecutionContextManager.ts"}, {"type": "file", "name": "RuleExecutionResultProcessor.ts"}, {"type": "file", "name": "RuleGovernanceComplianceValidator.ts"}, {"type": "file", "name": "RuleInheritanceChainManager.ts"}, {"type": "file", "name": "RulePerformanceOptimizationEngine.ts"}, {"type": "file", "name": "RulePriorityManagementSystem.ts"}]}, {"type": "directory", "name": "security-management", "contents": [{"type": "file", "name": "RuleAuditLogger.ts"}, {"type": "file", "name": "RuleIntegrityValidator.ts"}, {"type": "file", "name": "RuleSecurityFramework.ts"}, {"type": "file", "name": "RuleSecurityManager.ts"}]}]}, {"type": "directory", "name": "infrastructure"}, {"type": "directory", "name": "tracking", "contents": [{"type": "directory", "name": "advanced-data", "contents": [{"type": "file", "name": "ContextAuthorityProtocol.ts"}, {"type": "file", "name": "CrossReferenceValidationEngine.ts"}, {"type": "file", "name": "OrchestrationCoordinator.ts"}, {"type": "file", "name": "SmartPathResolutionSystem.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "ContextAuthorityProtocol.mem-safe.test.ts"}, {"type": "file", "name": "ContextAuthorityProtocol.test.ts"}, {"type": "file", "name": "ContextAuthorityProtocol.timing.test.ts"}, {"type": "file", "name": "CrossReferenceValidationEngine.test.ts"}, {"type": "file", "name": "CrossReferenceValidationEngine.timing.test.ts"}, {"type": "file", "name": "OrchestrationCoordinator.test.ts"}, {"type": "file", "name": "OrchestrationCoordinator.timing.test.ts"}, {"type": "file", "name": "SmartPathResolutionSystem.mem-safe.test.ts"}, {"type": "file", "name": "SmartPathResolutionSystem.test.ts"}, {"type": "file", "name": "SmartPathResolutionSystem.timing.test.ts"}]}]}, {"type": "directory", "name": "core-data", "contents": [{"type": "file", "name": "AnalyticsCacheManager.ts"}, {"type": "directory", "name": "audit"}, {"type": "directory", "name": "base", "contents": [{"type": "file", "name": "BaseTrackingService.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "BaseTrackingService.test.ts"}]}, {"type": "file", "name": "tst-report.md"}]}, {"type": "file", "name": "GovernanceLogTracker.ts"}, {"type": "file", "name": "ImplementationProgressTracker.ts"}, {"type": "file", "name": "SessionLogTracker.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "AnalyticsCacheManager.02.test.ts"}, {"type": "file", "name": "AnalyticsCacheManager.mem-safe.test.ts"}, {"type": "file", "name": "AnalyticsCacheManager.test.ts"}, {"type": "file", "name": "AnalyticsCacheManager.timing.test.ts"}, {"type": "file", "name": "ImplementationProgressTracker.mem-safe.test.ts"}, {"type": "file", "name": "ImplementationProgressTracker.security.test.ts"}, {"type": "file", "name": "ImplementationProgressTracker.test.ts"}, {"type": "file", "name": "SessionLogTracker.mem-safe.test.ts"}, {"type": "file", "name": "SessionLogTracker.rate-limits.test.ts"}, {"type": "file", "name": "SessionLogTracker.test.ts"}, {"type": "file", "name": "SessionLogTracker.timing.test.ts"}]}]}, {"type": "directory", "name": "core-managers", "contents": [{"type": "file", "name": "DashboardManager.ts"}, {"type": "file", "name": "FileManager.ts"}, {"type": "file", "name": "RealTimeManager.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "DashboardManager.mem-safe.test.ts"}, {"type": "file", "name": "DashboardManager.test.ts"}, {"type": "file", "name": "DashboardManager.timing.test.ts"}, {"type": "file", "name": "FileManager.functional.test.ts"}, {"type": "file", "name": "RealTimeManager.mem-safe.test.ts"}, {"type": "file", "name": "RealTimeManager.test.ts"}, {"type": "file", "name": "RealTimeManager.timing.test.ts"}, {"type": "file", "name": "TrackingManager.mem-safe.test.ts"}, {"type": "file", "name": "TrackingManager.test.ts"}, {"type": "file", "name": "TrackingManager.timing.test.ts"}]}, {"type": "file", "name": "TrackingManager.ts"}]}, {"type": "directory", "name": "core-trackers", "contents": [{"type": "file", "name": "AnalyticsTrackingEngine.ts"}, {"type": "file", "name": "AuthorityTrackingService.ts"}, {"type": "file", "name": "CrossReferenceTrackingEngine.ts"}, {"type": "file", "name": "GovernanceTrackingSystem.ts"}, {"type": "file", "name": "OrchestrationTrackingSystem.ts"}, {"type": "file", "name": "ProgressTrackingEngine.ts"}, {"type": "directory", "name": "security", "contents": [{"type": "file", "name": "ISecurityEnforcement.ts"}, {"type": "file", "name": "SecurityConfig.ts"}, {"type": "file", "name": "SecurityEnforcementLayer.ts"}]}, {"type": "file", "name": "SessionTrackingAudit.ts"}, {"type": "file", "name": "SessionTrackingCore.ts"}, {"type": "file", "name": "SessionTrackingRealtime.ts"}, {"type": "file", "name": "SessionTrackingUtils.ts"}, {"type": "file", "name": "SmartPathTrackingSystem.ts"}, {"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "AnalyticsTrackingEngine.test.ts"}, {"type": "file", "name": "AuthorityTrackingService.mem-safe.test.ts"}, {"type": "file", "name": "CrossReferenceTrackingEngine.test.ts"}, {"type": "file", "name": "GovernanceTrackingSystem.integration.test.ts"}, {"type": "file", "name": "GovernanceTrackingSystem.mem-safe.test.ts"}, {"type": "file", "name": "GovernanceTrackingSystem.mocks.ts"}, {"type": "file", "name": "GovernanceTrackingSystem.performance.test.ts"}, {"type": "file", "name": "GovernanceTrackingSystem.security.test.ts"}, {"type": "file", "name": "GovernanceTrackingSystem.test.setup.ts"}, {"type": "file", "name": "GovernanceTrackingSystem.test.ts"}, {"type": "file", "name": "GovernanceTrackingSystem.timing.test.ts"}, {"type": "file", "name": "OrchestrationTrackingSystem.test.ts"}, {"type": "file", "name": "ProgressTrackingEngine.test.ts"}, {"type": "file", "name": "SessionTrackingAudit.test.ts"}, {"type": "file", "name": "SessionTrackingCore.test.ts"}, {"type": "file", "name": "SessionTrackingRealtime.test.ts"}, {"type": "file", "name": "SessionTrackingUtils.test.ts"}]}]}, {"type": "directory", "name": "core-utils", "contents": [{"type": "directory", "name": "__tests__", "contents": [{"type": "file", "name": "TrackingUtilities.test.ts"}]}, {"type": "file", "name": "TrackingUtilities.ts"}]}]}]}, {"type": "directory", "name": "types", "contents": [{"type": "file", "name": "dependency-types.ts"}]}]}]}, {"type": "report", "directories": 57, "files": 210}]